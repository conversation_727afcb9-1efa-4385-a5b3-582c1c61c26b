package com.agentic.framework.mcp;

import com.agentic.framework.model.ModelConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Registry for managing different AI models and their configurations
 * Provides dynamic model loading and routing capabilities
 */
@Component
public class ModelRegistry {
    
    private static final Logger logger = LoggerFactory.getLogger(ModelRegistry.class);
    
    private final ConcurrentMap<String, ModelProvider> modelProviders = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, ModelConfig> modelConfigs = new ConcurrentHashMap<>();
    
    @Value("${mcp.models.openai.api-key:}")
    private String openaiApiKey;
    
    @Value("${mcp.models.openai.base-url:}")
    private String openaiBaseUrl;
    
    @Value("${mcp.models.anthropic.api-key:}")
    private String anthropicApiKey;
    
    @Value("${mcp.models.anthropic.base-url:}")
    private String anthropicBaseUrl;
    
    @Value("${mcp.models.local.base-url:}")
    private String localBaseUrl;
    
    public ModelRegistry() {
        initializeDefaultModels();
    }
    
    private void initializeDefaultModels() {
        // Register OpenAI models
        registerModel("gpt-4", new OpenAIModelProvider(openaiApiKey, openaiBaseUrl));
        registerModel("gpt-3.5-turbo", new OpenAIModelProvider(openaiApiKey, openaiBaseUrl));
        
        // Register Anthropic models
        registerModel("claude-3-sonnet", new AnthropicModelProvider(anthropicApiKey, anthropicBaseUrl));
        registerModel("claude-3-haiku", new AnthropicModelProvider(anthropicApiKey, anthropicBaseUrl));
        
        // Register local models
        registerModel("llama2", new LocalModelProvider(localBaseUrl));
        registerModel("mistral", new LocalModelProvider(localBaseUrl));
        
        logger.info("Model registry initialized with {} models", modelProviders.size());
    }
    
    public void registerModel(String modelName, ModelProvider provider) {
        modelProviders.put(modelName, provider);
        logger.info("Registered model: {}", modelName);
    }
    
    public void unregisterModel(String modelName) {
        ModelProvider removed = modelProviders.remove(modelName);
        if (removed != null) {
            logger.info("Unregistered model: {}", modelName);
        }
    }
    
    public ModelProvider getModelProvider(String modelName) {
        ModelProvider provider = modelProviders.get(modelName);
        if (provider == null) {
            logger.warn("Model not found: {}", modelName);
            throw new IllegalArgumentException("Model not found: " + modelName);
        }
        return provider;
    }
    
    public boolean hasModel(String modelName) {
        return modelProviders.containsKey(modelName);
    }
    
    public Map<String, ModelProvider> getAllModels() {
        return new ConcurrentHashMap<>(modelProviders);
    }
    
    public void setModelConfig(String modelName, ModelConfig config) {
        modelConfigs.put(modelName, config);
        logger.debug("Updated config for model: {}", modelName);
    }
    
    public ModelConfig getModelConfig(String modelName) {
        return modelConfigs.getOrDefault(modelName, new ModelConfig());
    }
    
    public int getModelCount() {
        return modelProviders.size();
    }
    
    public void refreshModels() {
        logger.info("Refreshing model registry...");
        modelProviders.clear();
        modelConfigs.clear();
        initializeDefaultModels();
        logger.info("Model registry refresh complete");
    }
}
