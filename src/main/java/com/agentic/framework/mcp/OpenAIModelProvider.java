package com.agentic.framework.mcp;

import com.agentic.framework.model.ModelConfig;
import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.agentic.framework.model.ResponseMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * OpenAI model provider implementation
 * Handles interactions with OpenAI's API endpoints
 */
public class OpenAIModelProvider implements ModelProvider {
    
    private static final Logger logger = LoggerFactory.getLogger(OpenAIModelProvider.class);
    
    private final String apiKey;
    private final String baseUrl;
    private final RestTemplate restTemplate;
    private final ModelInfo modelInfo;
    
    public OpenAIModelProvider(String apiKey, String baseUrl) {
        this.apiKey = apiKey;
        this.baseUrl = baseUrl != null && !baseUrl.isEmpty() ? baseUrl : "https://api.openai.com/v1";
        this.restTemplate = new RestTemplate();
        this.modelInfo = new ModelInfo("OpenAI", "GPT models", "text-generation", true);
    }
    
    @Override
    public String getModelName() {
        return "openai";
    }
    
    @Override
    public boolean isAvailable() {
        return apiKey != null && !apiKey.isEmpty();
    }
    
    @Override
    public ModelConfig getDefaultConfig() {
        ModelConfig config = new ModelConfig();
        config.setTemperature(0.7);
        config.setMaxTokens(4000);
        config.setTopP(1.0);
        config.setStream(false);
        return config;
    }
    
    @Override
    public CompletableFuture<ModelResponse> processRequest(ModelRequest request, ModelConfig config) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (!isAvailable()) {
                    throw new RuntimeException("OpenAI API key not configured");
                }
                
                // Prepare request payload
                Map<String, Object> payload = new HashMap<>();
                payload.put("model", request.getModelName());
                payload.put("messages", buildMessages(request));
                payload.put("temperature", config.getTemperature());
                payload.put("max_tokens", config.getMaxTokens());
                payload.put("top_p", config.getTopP());
                payload.put("stream", config.getStream());
                
                if (config.getStop() != null) {
                    payload.put("stop", List.of(config.getStop()));
                }
                
                // Make API call
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.setBearerAuth(apiKey);
                
                HttpEntity<Map<String, Object>> entity = new HttpEntity<>(payload, headers);
                String url = baseUrl + "/chat/completions";
                
                ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
                
                if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                    return parseOpenAIResponse(response.getBody(), request);
                } else {
                    throw new RuntimeException("OpenAI API call failed: " + response.getStatusCode());
                }
                
            } catch (Exception e) {
                logger.error("Error processing OpenAI request", e);
                ModelResponse errorResponse = new ModelResponse(request.getModelName(), "Error: " + e.getMessage());
                errorResponse.setStatus(ModelResponse.ResponseStatus.ERROR);
                return errorResponse;
            }
        });
    }
    
    @Override
    public ModelInfo getModelInfo() {
        return modelInfo;
    }
    
    private List<Map<String, String>> buildMessages(ModelRequest request) {
        List<Map<String, String>> messages = new java.util.ArrayList<>();
        
        // Add system message
        if (request.getSystemMessage() != null && !request.getSystemMessage().trim().isEmpty()) {
            Map<String, String> systemMsg = new HashMap<>();
            systemMsg.put("role", "system");
            systemMsg.put("content", request.getSystemMessage());
            messages.add(systemMsg);
        }
        
        // Add user messages
        for (String userMsg : request.getUserMessages()) {
            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", userMsg);
            messages.add(userMessage);
        }
        
        return messages;
    }
    
    private ModelResponse parseOpenAIResponse(Map responseBody, ModelRequest request) {
        ModelResponse response = new ModelResponse(request.getModelName(), "");
        
        try {
            List<Map> choices = (List<Map>) responseBody.get("choices");
            if (choices != null && !choices.isEmpty()) {
                Map firstChoice = choices.get(0);
                Map message = (Map) firstChoice.get("message");
                if (message != null) {
                    response.setResponse((String) message.get("content"));
                }
            }
            
            // Parse usage information
            Map usage = (Map) responseBody.get("usage");
            if (usage != null) {
                response.setTokensUsed((Integer) usage.get("total_tokens"));
            }
            
            // Set metadata
            ResponseMetadata metadata = new ResponseMetadata();
            metadata.setModel(request.getModelName());
            metadata.setProvider("OpenAI");
            metadata.setRequestId((String) responseBody.get("id"));
            response.setMetadata(metadata);
            
        } catch (Exception e) {
            logger.error("Error parsing OpenAI response", e);
            response.setResponse("Error parsing response");
            response.setStatus(ModelResponse.ResponseStatus.ERROR);
        }
        
        return response;
    }
    
    @Override
    public double estimateCost(ModelRequest request) {
        // Rough cost estimation for OpenAI models
        int estimatedTokens = request.getUserMessages().stream()
            .mapToInt(String::length)
            .sum() / 4; // Rough token estimation
        
        if (request.getModelName().contains("gpt-4")) {
            return estimatedTokens * 0.00003; // GPT-4 pricing
        } else {
            return estimatedTokens * 0.000002; // GPT-3.5 pricing
        }
    }
}
