#!/bin/bash

echo "🚀 Starting Agentic Framework..."

# Check if Java is installed
if ! command -v java &> /dev/null; then
    echo "❌ Java is not installed. Please install Java 17 or higher."
    exit 1
fi

# Check Java version
JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 17 ]; then
    echo "❌ Java 17 or higher is required. Current version: $JAVA_VERSION"
    exit 1
fi

echo "✅ Java version: $(java -version 2>&1 | head -n 1)"

# Check if Maven is installed
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven is not installed. Please install Maven."
    exit 1
fi

echo "✅ Maven version: $(mvn -version | head -n 1)"

# Create logs directory if it doesn't exist
mkdir -p logs
mkdir -p data

echo "📁 Created logs and data directories"

# Build the project
echo "🔨 Building project..."
mvn clean compile -q

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please check the errors above."
    exit 1
fi

echo "✅ Build successful"

# Start the application
echo "🌐 Starting Spring Boot application..."
echo "📡 REST API will be available at: http://localhost:8080"
echo "🔌 MCP Server will be available at: localhost:8081"
echo "📊 Health check: http://localhost:8080/api/v1/health"
echo ""
echo "Press Ctrl+C to stop the application"
echo ""

# Run the application
mvn spring-boot:run
