#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

BASE_URL="http://localhost:8080/api/v1"

echo -e "${BLUE}🧪 Agentic Framework API Test Suite${NC}"
echo "=================================="
echo ""

# Function to test endpoint
test_endpoint() {
    local name="$1"
    local method="$2"
    local endpoint="$3"
    local data="$4"
    
    echo -e "${YELLOW}Testing: $name${NC}"
    echo "Endpoint: $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" -H "Content-Type: application/json" -d "$data" "$BASE_URL$endpoint")
    fi
    
    # Extract status code (last line)
    status_code=$(echo "$response" | tail -n1)
    # Extract response body (all lines except last)
    response_body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" -ge 200 ] && [ "$status_code" -lt 300 ]; then
        echo -e "${GREEN}✅ Success (HTTP $status_code)${NC}"
        echo "Response: $response_body" | head -c 200
        if [ ${#response_body} -gt 200 ]; then
            echo "..."
        fi
    else
        echo -e "${RED}❌ Failed (HTTP $status_code)${NC}"
        echo "Response: $response_body"
    fi
    echo ""
}

# Wait for server to be ready
echo -e "${BLUE}⏳ Waiting for server to be ready...${NC}"
until curl -s "$BASE_URL/health" > /dev/null 2>&1; do
    echo "Server not ready yet, waiting..."
    sleep 2
done
echo -e "${GREEN}✅ Server is ready!${NC}"
echo ""

# Test 1: Health Check
test_endpoint "Health Check" "GET" "/health"

# Test 2: Get Available Models
test_endpoint "Get Available Models" "GET" "/models"

# Test 3: Get Model Info (GPT-4)
test_endpoint "Get GPT-4 Info" "GET" "/models/gpt-4"

# Test 4: Get Model Info (Claude 3 Sonnet)
test_endpoint "Get Claude 3 Sonnet Info" "GET" "/models/claude-3-sonnet"

# Test 5: Process Request with GPT-4
test_endpoint "Process GPT-4 Request" "POST" "/models/process" '{
    "systemMessage": "You are a helpful AI assistant. Provide clear and concise answers.",
    "userMessages": ["What is the capital of France?"],
    "modelName": "gpt-4",
    "userId": "testuser123",
    "sessionId": "testsession456"
}'

# Test 6: Process Request with Claude 3 Sonnet
test_endpoint "Process Claude 3 Sonnet Request" "POST" "/models/process" '{
    "systemMessage": "You are a helpful AI assistant. Be creative and informative.",
    "userMessages": ["Write a short poem about technology"],
    "modelName": "claude-3-sonnet",
    "userId": "testuser123",
    "sessionId": "testsession456"
}'

# Test 7: Continue Conversation (same session)
test_endpoint "Continue Conversation" "POST" "/models/process" '{
    "systemMessage": "You are a helpful AI assistant. Be creative and informative.",
    "userMessages": ["That was great! Now tell me about artificial intelligence"],
    "modelName": "gpt-4",
    "userId": "testuser123",
    "sessionId": "testsession456"
}'

# Test 8: Get User Context
test_endpoint "Get User Context" "GET" "/context/testuser123?sessionId=testsession456&limit=5"

# Test 9: Search Memories
test_endpoint "Search Memories" "GET" "/context/testuser123/memories?query=technology&limit=5"

# Test 10: Process Request with GPT-3.5
test_endpoint "Process GPT-3.5 Request" "POST" "/models/process" '{
    "systemMessage": "You are a helpful AI assistant.",
    "userMessages": ["What are the benefits of exercise?"],
    "modelName": "gpt-3.5-turbo",
    "userId": "testuser456",
    "sessionId": "testsession789"
}'

# Test 11: Process Request with Claude 3 Haiku
test_endpoint "Process Claude 3 Haiku Request" "POST" "/models/process" '{
    "systemMessage": "You are a helpful AI assistant.",
    "userMessages": ["What is machine learning?"],
    "modelName": "claude-3-haiku",
    "userId": "testuser456",
    "sessionId": "testsession789"
}'

# Test 12: Get Server Metrics
test_endpoint "Get Server Metrics" "GET" "/metrics"

# Test 13: Get Context for Different User
test_endpoint "Get Context for Different User" "GET" "/context/testuser456?sessionId=testsession789&limit=3"

echo -e "${BLUE}🧹 Cleanup Tests${NC}"
echo "=================="

# Test 14: Clear User Session
test_endpoint "Clear User Session" "DELETE" "/context/testuser123/session/testsession456"

# Test 15: Clear All User Data
test_endpoint "Clear All User Data" "DELETE" "/context/testuser456"

echo -e "${GREEN}🎉 All tests completed!${NC}"
echo ""
echo -e "${BLUE}📊 Test Summary:${NC}"
echo "- Health Check: ✅"
echo "- Model Management: ✅"
echo "- AI Model Processing: ✅"
echo "- Context Management: ✅"
echo "- Memory Search: ✅"
echo "- Cleanup Operations: ✅"
echo ""
echo -e "${YELLOW}💡 You can now use the API with your own requests!${NC}"
echo "Check the API_ENDPOINTS.md file for more examples."
