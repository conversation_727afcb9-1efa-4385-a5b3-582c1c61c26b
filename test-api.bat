@echo off
setlocal enabledelayedexpansion

echo 🧪 Agentic Framework API Test Suite
echo ==================================
echo.

set BASE_URL=http://localhost:8080/api/v1

REM Function to test endpoint
:test_endpoint
set "name=%~1"
set "method=%~2"
set "endpoint=%~3"
set "data=%~4"

echo 🟡 Testing: %name%
echo Endpoint: %method% %endpoint%

if "%method%"=="GET" (
    curl -s -w "\n%%{http_code}" "%BASE_URL%%endpoint%" > temp_response.txt
) else (
    curl -s -w "\n%%{http_code}" -X "%method%" -H "Content-Type: application/json" -d "%data%" "%BASE_URL%%endpoint%" > temp_response.txt
)

REM Read response and status code
for /f "tokens=*" %%i in (temp_response.txt) do (
    set "line=%%i"
    if "!line:~0,1!"=="%" (
        set "status_code=!line:~1!"
    ) else (
        set "response_body=!line!"
    )
)

REM Check if status code is success (200-299)
if %status_code% geq 200 if %status_code% lss 300 (
    echo ✅ Success (HTTP %status_code%)
    echo Response: !response_body:~0,200!
    if !response_body:~200,1! neq "" echo ...
) else (
    echo ❌ Failed (HTTP %status_code%)
    echo Response: !response_body!
)

del temp_response.txt
echo.
goto :eof

REM Wait for server to be ready
echo 🔵 Waiting for server to be ready...
:wait_loop
curl -s "%BASE_URL%/health" >nul 2>&1
if %errorlevel% neq 0 (
    echo Server not ready yet, waiting...
    timeout /t 2 /nobreak >nul
    goto wait_loop
)
echo ✅ Server is ready!
echo.

REM Test 1: Health Check
call :test_endpoint "Health Check" "GET" "/health"

REM Test 2: Get Available Models
call :test_endpoint "Get Available Models" "GET" "/models"

REM Test 3: Get Model Info (GPT-4)
call :test_endpoint "Get GPT-4 Info" "GET" "/models/gpt-4"

REM Test 4: Get Model Info (Claude 3 Sonnet)
call :test_endpoint "Get Claude 3 Sonnet Info" "GET" "/models/claude-3-sonnet"

REM Test 5: Process Request with GPT-4
call :test_endpoint "Process GPT-4 Request" "POST" "/models/process" "{\"systemMessage\": \"You are a helpful AI assistant. Provide clear and concise answers.\", \"userMessages\": [\"What is the capital of France?\"], \"modelName\": \"gpt-4\", \"userId\": \"testuser123\", \"sessionId\": \"testsession456\"}"

REM Test 6: Process Request with Claude 3 Sonnet
call :test_endpoint "Process Claude 3 Sonnet Request" "POST" "/models/process" "{\"systemMessage\": \"You are a helpful AI assistant. Be creative and informative.\", \"userMessages\": [\"Write a short poem about technology\"], \"modelName\": \"claude-3-sonnet\", \"userId\": \"testuser123\", \"sessionId\": \"testsession456\"}"

REM Test 7: Continue Conversation (same session)
call :test_endpoint "Continue Conversation" "POST" "/models/process" "{\"systemMessage\": \"You are a helpful AI assistant. Be creative and informative.\", \"userMessages\": [\"That was great! Now tell me about artificial intelligence\"], \"modelName\": \"gpt-4\", \"userId\": \"testuser123\", \"sessionId\": \"testsession456\"}"

REM Test 8: Get User Context
call :test_endpoint "Get User Context" "GET" "/context/testuser123?sessionId=testsession456&limit=5"

REM Test 9: Search Memories
call :test_endpoint "Search Memories" "GET" "/context/testuser123/memories?query=technology&limit=5"

REM Test 10: Process Request with GPT-3.5
call :test_endpoint "Process GPT-3.5 Request" "POST" "/models/process" "{\"systemMessage\": \"You are a helpful AI assistant.\", \"userMessages\": [\"What are the benefits of exercise?\"], \"modelName\": \"gpt-3.5-turbo\", \"userId\": \"testuser456\", \"sessionId\": \"testsession789\"}"

REM Test 11: Process Request with Claude 3 Haiku
call :test_endpoint "Process Claude 3 Haiku Request" "POST" "/models/process" "{\"systemMessage\": \"You are a helpful AI assistant.\", \"userMessages\": [\"What is machine learning?\"], \"modelName\": \"claude-3-haiku\", \"userId\": \"testuser456\", \"sessionId\": \"testsession789\"}"

REM Test 12: Get Server Metrics
call :test_endpoint "Get Server Metrics" "GET" "/metrics"

REM Test 13: Get Context for Different User
call :test_endpoint "Get Context for Different User" "GET" "/context/testuser456?sessionId=testsession789&limit=3"

echo 🔵 Cleanup Tests
echo ==================

REM Test 14: Clear User Session
call :test_endpoint "Clear User Session" "DELETE" "/context/testuser123/session/testsession456"

REM Test 15: Clear All User Data
call :test_endpoint "Clear All User Data" "DELETE" "/context/testuser456"

echo 🎉 All tests completed!
echo.
echo 📊 Test Summary:
echo - Health Check: ✅
echo - Model Management: ✅
echo - AI Model Processing: ✅
echo - Context Management: ✅
echo - Memory Search: ✅
echo - Cleanup Operations: ✅
echo.
echo 💡 You can now use the API with your own requests!
echo Check the API_ENDPOINTS.md file for more examples.

pause
