package com.agentic.framework.mcp;

import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.Socket;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Handles individual client connections in the MCP server
 * Manages communication protocol and request processing
 */
public class ClientHandler implements Runnable {
    
    private static final Logger logger = LoggerFactory.getLogger(ClientHandler.class);
    
    private final Socket clientSocket;
    private final ModelRegistry modelRegistry;
    private final ContextManager contextManager;
    private final ObjectMapper objectMapper;
    private final String clientId;
    
    public ClientHandler(Socket clientSocket, ModelRegistry modelRegistry, ContextManager contextManager) {
        this.clientSocket = clientSocket;
        this.modelRegistry = modelRegistry;
        this.contextManager = contextManager;
        this.objectMapper = new ObjectMapper();
        this.clientId = UUID.randomUUID().toString();
    }
    
    @Override
    public void run() {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
             PrintWriter writer = new PrintWriter(clientSocket.getOutputStream(), true)) {
            
            logger.info("Client handler started for client: {}", clientId);
            
            String inputLine;
            while ((inputLine = reader.readLine()) != null) {
                try {
                    // Parse the incoming request
                    McpRequest request = objectMapper.readValue(inputLine, McpRequest.class);
                    
                    // Process the request
                    McpResponse response = processRequest(request);
                    
                    // Send response back to client
                    String responseJson = objectMapper.writeValueAsString(response);
                    writer.println(responseJson);
                    
                } catch (Exception e) {
                    logger.error("Error processing client request", e);
                    
                    // Send error response
                    McpResponse errorResponse = new McpResponse();
                    errorResponse.setSuccess(false);
                    errorResponse.setError("Error processing request: " + e.getMessage());
                    
                    String errorJson = objectMapper.writeValueAsString(errorResponse);
                    writer.println(errorJson);
                }
            }
            
        } catch (IOException e) {
            logger.error("Error in client handler for client: {}", clientId, e);
        } finally {
            try {
                clientSocket.close();
                logger.info("Client connection closed for client: {}", clientId);
            } catch (IOException e) {
                logger.error("Error closing client socket", e);
            }
        }
    }
    
    private McpResponse processRequest(McpRequest request) {
        McpResponse response = new McpResponse();
        
        try {
            switch (request.getType()) {
                case "model_request":
                    response = handleModelRequest(request);
                    break;
                case "get_models":
                    response = handleGetModels(request);
                    break;
                case "get_context":
                    response = handleGetContext(request);
                    break;
                case "clear_context":
                    response = handleClearContext(request);
                    break;
                default:
                    response.setSuccess(false);
                    response.setError("Unknown request type: " + request.getType());
            }
        } catch (Exception e) {
            logger.error("Error processing request type: {}", request.getType(), e);
            response.setSuccess(false);
            response.setError("Internal error: " + e.getMessage());
        }
        
        return response;
    }
    
    private McpResponse handleModelRequest(McpRequest request) {
        McpResponse response = new McpResponse();
        
        try {
            // Extract model request data
            ModelRequest modelRequest = objectMapper.convertValue(request.getData(), ModelRequest.class);
            
            // Generate session ID if not provided
            if (modelRequest.getSessionId() == null) {
                modelRequest.setSessionId(UUID.randomUUID().toString());
            }
            
            // Get model provider and process request
            var modelProvider = modelRegistry.getModelProvider(modelRequest.getModelName());
            CompletableFuture<ModelResponse> futureResponse = modelProvider.processRequest(modelRequest);
            
            // Wait for response (in production, this should be async)
            ModelResponse modelResponse = futureResponse.get();
            
            // Build response
            response.setSuccess(true);
            response.setData(modelResponse);
            
        } catch (Exception e) {
            logger.error("Error handling model request", e);
            response.setSuccess(false);
            response.setError("Model request failed: " + e.getMessage());
        }
        
        return response;
    }
    
    private McpResponse handleGetModels(McpRequest request) {
        McpResponse response = new McpResponse();
        
        try {
            var models = modelRegistry.getAllModels();
            response.setSuccess(true);
            response.setData(models);
            
        } catch (Exception e) {
            logger.error("Error getting models", e);
            response.setSuccess(false);
            response.setError("Failed to get models: " + e.getMessage());
        }
        
        return response;
    }
    
    private McpResponse handleGetContext(McpRequest request) {
        McpResponse response = new McpResponse();
        
        try {
            String userId = (String) request.getData().get("userId");
            String sessionId = (String) request.getData().get("sessionId");
            Integer limit = (Integer) request.getData().getOrDefault("limit", 10);
            
            var context = contextManager.getContext(userId, sessionId, limit);
            response.setSuccess(true);
            response.setData(context);
            
        } catch (Exception e) {
            logger.error("Error getting context", e);
            response.setSuccess(false);
            response.setError("Failed to get context: " + e.getMessage());
        }
        
        return response;
    }
    
    private McpResponse handleClearContext(McpRequest request) {
        McpResponse response = new McpResponse();
        
        try {
            String userId = (String) request.getData().get("userId");
            String sessionId = (String) request.getData().get("sessionId");
            
            if (sessionId != null) {
                contextManager.clearContext(userId, sessionId);
            } else {
                contextManager.clearUserData(userId);
            }
            
            response.setSuccess(true);
            response.setData("Context cleared successfully");
            
        } catch (Exception e) {
            logger.error("Error clearing context", e);
            response.setSuccess(false);
            response.setError("Failed to clear context: " + e.getMessage());
        }
        
        return response;
    }
    
    // Inner classes for MCP protocol
    private static class McpRequest {
        private String type;
        private Object data;
        
        // Getters and setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
    }
    
    private static class McpResponse {
        private boolean success;
        private Object data;
        private String error;
        
        // Getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
    }
}
