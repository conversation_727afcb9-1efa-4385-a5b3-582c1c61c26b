@echo off
echo 🚀 Starting Agentic Framework...

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java is not installed. Please install Java 17 or higher.
    pause
    exit /b 1
)

REM Check Java version (basic check)
java -version 2>&1 | findstr "version" >nul
if %errorlevel% neq 0 (
    echo ❌ Java version check failed.
    pause
    exit /b 1
)

echo ✅ Java is installed

REM Check if <PERSON><PERSON> is installed
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Maven is not installed. Please install Maven.
    pause
    exit /b 1
)

echo ✅ Maven is installed

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs
if not exist "data" mkdir data

echo 📁 Created logs and data directories

REM Build the project
echo 🔨 Building project...
mvn clean compile -q

if %errorlevel% neq 0 (
    echo ❌ Build failed. Please check the errors above.
    pause
    exit /b 1
)

echo ✅ Build successful

REM Start the application
echo 🌐 Starting Spring Boot application...
echo 📡 REST API will be available at: http://localhost:8080
echo 🔌 MCP Server will be available at: localhost:8081
echo 📊 Health check: http://localhost:8080/api/v1/health
echo.
echo Press Ctrl+C to stop the application
echo.

REM Run the application
mvn spring-boot:run

pause
