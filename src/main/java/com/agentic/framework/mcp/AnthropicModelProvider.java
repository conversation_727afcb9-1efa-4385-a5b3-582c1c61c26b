package com.agentic.framework.mcp;

import com.agentic.framework.model.ModelConfig;
import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.agentic.framework.model.ResponseMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Anthropic model provider implementation
 * Handles interactions with Anthropic's Claude API endpoints
 */
public class AnthropicModelProvider implements ModelProvider {
    
    private static final Logger logger = LoggerFactory.getLogger(AnthropicModelProvider.class);
    
    private final String apiKey;
    private final String baseUrl;
    private final RestTemplate restTemplate;
    private final ModelInfo modelInfo;
    
    public AnthropicModelProvider(String apiKey, String baseUrl) {
        this.apiKey = apiKey;
        this.baseUrl = baseUrl != null && !baseUrl.isEmpty() ? baseUrl : "https://api.anthropic.com/v1";
        this.restTemplate = new RestTemplate();
        this.modelInfo = new ModelInfo("Anthropic", "Claude models", "text-generation", true);
    }
    
    @Override
    public String getModelName() {
        return "anthropic";
    }
    
    @Override
    public boolean isAvailable() {
        return apiKey != null && !apiKey.isEmpty();
    }
    
    @Override
    public ModelConfig getDefaultConfig() {
        ModelConfig config = new ModelConfig();
        config.setTemperature(0.7);
        config.setMaxTokens(4000);
        config.setTopP(1.0);
        config.setStream(false);
        return config;
    }
    
    @Override
    public CompletableFuture<ModelResponse> processRequest(ModelRequest request, ModelConfig config) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (!isAvailable()) {
                    throw new RuntimeException("Anthropic API key not configured");
                }
                
                // Prepare request payload
                Map<String, Object> payload = new HashMap<>();
                payload.put("model", request.getModelName());
                payload.put("max_tokens", config.getMaxTokens());
                payload.put("temperature", config.getTemperature());
                payload.put("top_p", config.getTopP());
                payload.put("stream", config.getStream());
                
                // Build messages in Anthropic format
                StringBuilder prompt = new StringBuilder();
                if (request.getSystemMessage() != null && !request.getSystemMessage().trim().isEmpty()) {
                    prompt.append("System: ").append(request.getSystemMessage()).append("\n\n");
                }
                
                for (String userMsg : request.getUserMessages()) {
                    prompt.append("Human: ").append(userMsg).append("\n\n");
                }
                prompt.append("Assistant:");
                
                payload.put("prompt", prompt.toString());
                
                // Make API call
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.set("x-api-key", apiKey);
                headers.set("anthropic-version", "2023-06-01");
                
                HttpEntity<Map<String, Object>> entity = new HttpEntity<>(payload, headers);
                String url = baseUrl + "/complete";
                
                ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
                
                if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                    return parseAnthropicResponse(response.getBody(), request);
                } else {
                    throw new RuntimeException("Anthropic API call failed: " + response.getStatusCode());
                }
                
            } catch (Exception e) {
                logger.error("Error processing Anthropic request", e);
                ModelResponse errorResponse = new ModelResponse(request.getModelName(), "Error: " + e.getMessage());
                errorResponse.setStatus(ModelResponse.ResponseStatus.ERROR);
                return errorResponse;
            }
        });
    }
    
    @Override
    public ModelInfo getModelInfo() {
        return modelInfo;
    }
    
    private ModelResponse parseAnthropicResponse(Map responseBody, ModelRequest request) {
        ModelResponse response = new ModelResponse(request.getModelName(), "");
        
        try {
            String completion = (String) responseBody.get("completion");
            if (completion != null) {
                response.setResponse(completion.trim());
            }
            
            // Parse usage information
            Map usage = (Map) responseBody.get("usage");
            if (usage != null) {
                Integer inputTokens = (Integer) usage.get("input_tokens");
                Integer outputTokens = (Integer) usage.get("output_tokens");
                if (inputTokens != null && outputTokens != null) {
                    response.setTokensUsed(inputTokens + outputTokens);
                }
            }
            
            // Set metadata
            ResponseMetadata metadata = new ResponseMetadata();
            metadata.setModel(request.getModelName());
            metadata.setProvider("Anthropic");
            metadata.setRequestId((String) responseBody.get("id"));
            response.setMetadata(metadata);
            
        } catch (Exception e) {
            logger.error("Error parsing Anthropic response", e);
            response.setResponse("Error parsing response");
            response.setStatus(ModelResponse.ResponseStatus.ERROR);
        }
        
        return response;
    }
    
    @Override
    public double estimateCost(ModelRequest request) {
        // Rough cost estimation for Anthropic models
        int estimatedTokens = request.getUserMessages().stream()
            .mapToInt(String::length)
            .sum() / 4; // Rough token estimation
        
        if (request.getModelName().contains("claude-3")) {
            return estimatedTokens * 0.000015; // Claude 3 pricing
        } else {
            return estimatedTokens * 0.000008; // Claude 2 pricing
        }
    }
}
